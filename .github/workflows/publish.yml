name: Publish Python distribution to PyPI

on:
  release:
    types: [created]

jobs:
  pypi-publish:
    name: Publish release to PyPI
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/parmancer
    permissions:
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          version: "0.6.6"
          enable-cache: true
      - name: Install dependencies
        run: uv sync
      - name: Build dist
        run: uv build
      - name: Publish distribution 📦 to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
