<svg class="railroad-diagram" height="761" viewBox="0 0 1258.0 761" width="1258.0" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g transform="translate(.5 .5)">
<g>
<path d="M20 69v20m10 -20v20m-10 -10h20" /></g><path d="M40 79h10" /><g>
<path d="M50 79h0.0" /><path d="M1208.0 723h0.0" /><rect class="group-box" height="705" rx="10" ry="10" width="1158" x="50" y="36"></rect><g>
<path d="M50.0 79h10.0" /><path d="M60.0 79h10" /><g>
<path d="M70.0 79h454.0" /><path d="M734.0 79h454.0" /><rect class="group-box" height="38" rx="10" ry="10" width="210" x="524" y="60"></rect><g class="non-terminal ">
<path d="M524.0 79h10.0" /><path d="M724.0 79h10.0" /><rect height="22" width="190" x="534" y="68"></rect><text x="629" y="83">[\s\S]*?(?=School =)</text></g><g class="non-terminal ">
<path d="M524.0 52h0.0" /><path d="M618.0 52h0.0" /><text class="comment" x="571" y="57">field:header</text></g></g><path d="M1188.0 79a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-1118.0a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10" /><g>
<path d="M70.0 205h10.0" /><path d="M1178.0 723h10.0" /><rect class="group-box" height="603" rx="10" ry="10" width="1098" x="80" y="130"></rect><g>
<path d="M80.0 205h0.0" /><path d="M1178.0 723h0.0" /><path d="M80.0 205a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path d="M100.0 138h1058.0" /></g><path d="M1158.0 138a10 10 0 0 1 10 10v565a10 10 0 0 0 10 10" /><path d="M80.0 205h20" /><g>
<path d="M100.0 205h0.0" /><path d="M1158.0 723h0.0" /><path d="M100.0 205h10" /><g>
<path d="M110.0 205h0.0" /><path d="M1148.0 723h0.0" /><rect class="group-box" height="579" rx="10" ry="10" width="1038" x="110" y="162"></rect><g>
<path d="M110.0 205h10.0" /><path d="M120.0 205h10" /><g>
<path d="M130.0 205h351.0" /><path d="M777.0 205h351.0" /><rect class="group-box" height="38" rx="10" ry="10" width="296" x="481" y="186"></rect><g>
<path d="M481.0 205h10.0" /><path d="M767.0 205h10.0" /><g>
<path d="M491.0 205h0.0" /><g class="terminal ">
<path d="M491.0 205h10.0" /><path d="M597.5 205h10.0" /><rect height="22" rx="10" ry="10" width="96.5" x="501" y="194"></rect><text x="549.25" y="209">School = </text></g><path d="M607.5 205h0.0" /></g><path d="M607.5 205h10" /><path d="M617.5 205h10" /><g class="non-terminal ">
<path d="M627.5 205h0.0" /><path d="M698.5 205h0.0" /><rect height="22" width="71" x="627.5" y="194"></rect><text x="663" y="209">[^\n]+</text></g><path d="M698.5 205h10" /><path d="M708.5 205h10" /><g>
<path d="M718.5 205h0.0" /><g class="terminal ">
<path d="M718.5 205h10.0" /><path d="M757.0 205h10.0" /><rect height="22" rx="10" ry="10" width="28.5" x="728.5" y="194"></rect><text x="742.75" y="209">
</text></g><path d="M767.0 205h0.0" /></g></g><g class="non-terminal ">
<path d="M481.0 178h0.0" /><path d="M575.0 178h0.0" /><text class="comment" x="528" y="183">field:school</text></g></g><path d="M1128.0 205a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-998.0a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10" /><g>
<path d="M130.0 331h10.0" /><path d="M1118.0 723h10.0" /><rect class="group-box" height="477" rx="10" ry="10" width="978" x="140" y="256"></rect><g>
<path d="M140.0 331h0.0" /><path d="M1118.0 723h0.0" /><path d="M140.0 331a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path d="M160.0 264h938.0" /></g><path d="M1098.0 264a10 10 0 0 1 10 10v439a10 10 0 0 0 10 10" /><path d="M140.0 331h20" /><g>
<path d="M160.0 331h0.0" /><path d="M1098.0 723h0.0" /><path d="M160.0 331h10" /><g>
<path d="M170.0 331h0.0" /><path d="M1088.0 723h0.0" /><g>
<path d="M170.0 331h0.0" /><path d="M911.0 723h0.0" /><rect class="group-box" height="462" rx="10" ry="10" width="741" x="170" y="288"></rect><g>
<path d="M170.0 331h10.0" /><path d="M180.0 331h10" /><g>
<path d="M190.0 331h176.5" /><path d="M714.5 331h176.5" /><rect class="group-box" height="38" rx="10" ry="10" width="348" x="366.5" y="312"></rect><g>
<path d="M366.5 331h10.0" /><path d="M704.5 331h10.0" /><g>
<path d="M376.5 331h0.0" /><g class="terminal ">
<path d="M376.5 331h10.0" /><path d="M474.5 331h10.0" /><rect height="22" rx="10" ry="10" width="88" x="386.5" y="320"></rect><text x="430.5" y="335">Grade = </text></g><path d="M484.5 331h0.0" /></g><path d="M484.5 331h10" /><path d="M494.5 331h10" /><g>
<path d="M504.5 331h0.0" /><path d="M636.0 331h0.0" /><g class="non-terminal ">
<path d="M504.5 331h0.0" /><path d="M550.0 331h0.0" /><rect height="22" width="45.5" x="504.5" y="320"></rect><text x="527.25" y="335">\d+</text></g><path d="M550.0 331h10" /><path d="M560.0 331h10" /><g class="non-terminal ">
<path d="M570.0 331h0.0" /><path d="M636.0 331h0.0" /><text class="comment" x="603" y="336">map: int</text></g></g><path d="M636.0 331h10" /><path d="M646.0 331h10" /><g>
<path d="M656.0 331h0.0" /><g class="terminal ">
<path d="M656.0 331h10.0" /><path d="M694.5 331h10.0" /><rect height="22" rx="10" ry="10" width="28.5" x="666" y="320"></rect><text x="680.25" y="335">
</text></g><path d="M704.5 331h0.0" /></g></g><g class="non-terminal ">
<path d="M366.5 304h0.0" /><path d="M453.5 304h0.0" /><text class="comment" x="410" y="309">field:grade</text></g></g><path d="M891.0 331a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-701.0a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10" /><g>
<path d="M190.0 457h14.25" /><path d="M876.75 527h14.25" /><rect class="group-box" height="164" rx="10" ry="10" width="672.5" x="204.25" y="382"></rect><g>
<path d="M204.25 457h10.0" /><path d="M866.75 527h10.0" /><g>
<path d="M214.25 457h0.0" /><g class="terminal ">
<path d="M214.25 457h10.0" /><path d="M422.75 457h10.0" /><rect height="22" rx="10" ry="10" width="198.5" x="224.25" y="446"></rect><text x="323.5" y="461">Student number, Name
</text></g><path d="M432.75 457h0.0" /></g><path d="M432.75 457h10" /><g>
<path d="M442.75 457h0.0" /><path d="M791.25 527h0.0" /><path d="M442.75 457a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path d="M462.75 390h308.5" /></g><path d="M771.25 390a10 10 0 0 1 10 10v117a10 10 0 0 0 10 10" /><path d="M442.75 457h20" /><g>
<path d="M462.75 457h0.0" /><path d="M771.25 527h0.0" /><path d="M462.75 457h10" /><g>
<path d="M472.75 457h0.0" /><path d="M761.25 527h0.0" /><rect class="group-box" height="140" rx="10" ry="10" width="288.5" x="472.75" y="414"></rect><g>
<path d="M472.75 457h10.0" /><path d="M482.75 457h10" /><g>
<path d="M492.75 457h10.0" /><path d="M731.25 457h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="228.5" x="502.75" y="438"></rect><g>
<path d="M502.75 457h10.0" /><path d="M721.25 457h10.0" /><g>
<path d="M512.75 457h0.0" /><path d="M644.25 457h0.0" /><g class="non-terminal ">
<path d="M512.75 457h0.0" /><path d="M558.25 457h0.0" /><rect height="22" width="45.5" x="512.75" y="446"></rect><text x="535.5" y="461">\d+</text></g><path d="M558.25 457h10" /><path d="M568.25 457h10" /><g class="non-terminal ">
<path d="M578.25 457h0.0" /><path d="M644.25 457h0.0" /><text class="comment" x="611.25" y="462">map: int</text></g></g><path d="M644.25 457h10" /><path d="M654.25 457h10" /><g>
<path d="M664.25 457h0.0" /><g class="terminal ">
<path d="M664.25 457h10.0" /><path d="M711.25 457h10.0" /><rect height="22" rx="10" ry="10" width="37" x="674.25" y="446"></rect><text x="692.75" y="461">, </text></g><path d="M721.25 457h0.0" /></g></g><g class="non-terminal ">
<path d="M502.75 430h0.0" /><path d="M596.75 430h0.0" /><text class="comment" x="549.75" y="435">field:number</text></g></g><path d="M741.25 457a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-248.5a10 10 0 0 0 -10 10v23a10 10 0 0 0 10 10" /><g>
<path d="M492.75 527h44.5" /><path d="M696.75 527h44.5" /><rect class="group-box" height="38" rx="10" ry="10" width="159.5" x="537.25" y="508"></rect><g>
<path d="M537.25 527h10.0" /><path d="M686.75 527h10.0" /><g class="non-terminal ">
<path d="M547.25 527h0.0" /><path d="M618.25 527h0.0" /><rect height="22" width="71" x="547.25" y="516"></rect><text x="582.75" y="531">[^\n]+</text></g><path d="M618.25 527h10" /><path d="M628.25 527h10" /><g>
<path d="M638.25 527h0.0" /><g class="terminal ">
<path d="M638.25 527h10.0" /><path d="M676.75 527h10.0" /><rect height="22" rx="10" ry="10" width="28.5" x="648.25" y="516"></rect><text x="662.5" y="531">
</text></g><path d="M686.75 527h0.0" /></g></g><g class="non-terminal ">
<path d="M537.25 500h0.0" /><path d="M617.25 500h0.0" /><text class="comment" x="577.25" y="505">field:name</text></g></g><path d="M741.25 527h10" /><path d="M751.25 527h10.0" /></g><g class="non-terminal ">
<path d="M472.75 406h0.0" /><path d="M601.75 406h0.0" /><text class="comment" x="537.25" y="411">dataclass:Student</text></g></g><path d="M761.25 527h10" /><path d="M472.75 457a10 10 0 0 0 -10 10v85a10 10 0 0 0 10 10" /><g>
<path d="M472.75 562h288.5" /></g><path d="M761.25 562a10 10 0 0 0 10 -10v-15a10 10 0 0 0 -10 -10" /></g><path d="M771.25 527h20" /></g><path d="M791.25 527h10" /><g>
<path d="M801.25 527h0.0" /><g class="non-terminal ">
<path d="M801.25 527h10.0" /><path d="M856.75 527h10.0" /><rect height="22" width="45.5" x="811.25" y="516"></rect><text x="834" y="531">\n*</text></g><path d="M866.75 527h0.0" /></g></g><g class="non-terminal ">
<path d="M204.25 374h0.0" /><path d="M312.25 374h0.0" /><text class="comment" x="258.25" y="379">field:students</text></g></g><path d="M891.0 527a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-701.0a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10" /><g>
<path d="M190.0 653h10.0" /><path d="M881.0 723h10.0" /><rect class="group-box" height="164" rx="10" ry="10" width="681" x="200" y="578"></rect><g>
<path d="M200.0 653h10.0" /><path d="M871.0 723h10.0" /><g>
<path d="M210.0 653h0.0" /><g class="terminal ">
<path d="M210.0 653h10.0" /><path d="M427.0 653h10.0" /><rect height="22" rx="10" ry="10" width="207" x="220" y="642"></rect><text x="323.5" y="657">Student number, Score
</text></g><path d="M437.0 653h0.0" /></g><path d="M437.0 653h10" /><g>
<path d="M447.0 653h0.0" /><path d="M795.5 723h0.0" /><path d="M447.0 653a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path d="M467.0 586h308.5" /></g><path d="M775.5 586a10 10 0 0 1 10 10v117a10 10 0 0 0 10 10" /><path d="M447.0 653h20" /><g>
<path d="M467.0 653h0.0" /><path d="M775.5 723h0.0" /><path d="M467.0 653h10" /><g>
<path d="M477.0 653h0.0" /><path d="M765.5 723h0.0" /><rect class="group-box" height="140" rx="10" ry="10" width="288.5" x="477" y="610"></rect><g>
<path d="M477.0 653h10.0" /><path d="M487.0 653h10" /><g>
<path d="M497.0 653h10.0" /><path d="M735.5 653h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="228.5" x="507" y="634"></rect><g>
<path d="M507.0 653h10.0" /><path d="M725.5 653h10.0" /><g>
<path d="M517.0 653h0.0" /><path d="M648.5 653h0.0" /><g class="non-terminal ">
<path d="M517.0 653h0.0" /><path d="M562.5 653h0.0" /><rect height="22" width="45.5" x="517" y="642"></rect><text x="539.75" y="657">\d+</text></g><path d="M562.5 653h10" /><path d="M572.5 653h10" /><g class="non-terminal ">
<path d="M582.5 653h0.0" /><path d="M648.5 653h0.0" /><text class="comment" x="615.5" y="658">map: int</text></g></g><path d="M648.5 653h10" /><path d="M658.5 653h10" /><g>
<path d="M668.5 653h0.0" /><g class="terminal ">
<path d="M668.5 653h10.0" /><path d="M715.5 653h10.0" /><rect height="22" rx="10" ry="10" width="37" x="678.5" y="642"></rect><text x="697" y="657">, </text></g><path d="M725.5 653h0.0" /></g></g><g class="non-terminal ">
<path d="M507.0 626h0.0" /><path d="M601.0 626h0.0" /><text class="comment" x="554" y="631">field:number</text></g></g><path d="M745.5 653a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-248.5a10 10 0 0 0 -10 10v23a10 10 0 0 0 10 10" /><g>
<path d="M497.0 723h14.25" /><path d="M731.25 723h14.25" /><rect class="group-box" height="38" rx="10" ry="10" width="220" x="511.25" y="704"></rect><g>
<path d="M511.25 723h10.0" /><path d="M721.25 723h10.0" /><g>
<path d="M521.25 723h0.0" /><path d="M652.75 723h0.0" /><g class="non-terminal ">
<path d="M521.25 723h0.0" /><path d="M566.75 723h0.0" /><rect height="22" width="45.5" x="521.25" y="712"></rect><text x="544" y="727">\d+</text></g><path d="M566.75 723h10" /><path d="M576.75 723h10" /><g class="non-terminal ">
<path d="M586.75 723h0.0" /><path d="M652.75 723h0.0" /><text class="comment" x="619.75" y="728">map: int</text></g></g><path d="M652.75 723h10" /><path d="M662.75 723h10" /><g>
<path d="M672.75 723h0.0" /><g class="terminal ">
<path d="M672.75 723h10.0" /><path d="M711.25 723h10.0" /><rect height="22" rx="10" ry="10" width="28.5" x="682.75" y="712"></rect><text x="697" y="727">
</text></g><path d="M721.25 723h0.0" /></g></g><g class="non-terminal ">
<path d="M511.25 696h0.0" /><path d="M598.25 696h0.0" /><text class="comment" x="554.75" y="701">field:score</text></g></g><path d="M745.5 723h10" /><path d="M755.5 723h10.0" /></g><g class="non-terminal ">
<path d="M477.0 602h0.0" /><path d="M592.0 602h0.0" /><text class="comment" x="534.5" y="607">dataclass:Score</text></g></g><path d="M765.5 723h10" /><path d="M477.0 653a10 10 0 0 0 -10 10v85a10 10 0 0 0 10 10" /><g>
<path d="M477.0 758h288.5" /></g><path d="M765.5 758a10 10 0 0 0 10 -10v-15a10 10 0 0 0 -10 -10" /></g><path d="M775.5 723h20" /></g><path d="M795.5 723h10" /><g>
<path d="M805.5 723h0.0" /><g class="non-terminal ">
<path d="M805.5 723h10.0" /><path d="M861.0 723h10.0" /><rect height="22" width="45.5" x="815.5" y="712"></rect><text x="838.25" y="727">\n*</text></g><path d="M871.0 723h0.0" /></g></g><g class="non-terminal ">
<path d="M200.0 570h0.0" /><path d="M294.0 570h0.0" /><text class="comment" x="247" y="575">field:scores</text></g></g><path d="M891.0 723h10" /><path d="M901.0 723h10.0" /></g><g class="non-terminal ">
<path d="M170.0 280h0.0" /><path d="M320.0 280h0.0" /><text class="comment" x="245" y="285">dataclass:GradeInput</text></g></g><path d="M911.0 723h10" /><path d="M921.0 723h10" /><g class="non-terminal ">
<path d="M931.0 723h0.0" /><path d="M1088.0 723h0.0" /><text class="comment" x="1009.5" y="728">map: from_input_grade</text></g></g><path d="M1088.0 723h10" /><path d="M170.0 331a10 10 0 0 0 -10 10v407a10 10 0 0 0 10 10" /><g>
<path d="M170.0 758h918.0" /></g><path d="M1088.0 758a10 10 0 0 0 10 -10v-15a10 10 0 0 0 -10 -10" /></g><path d="M1098.0 723h20" /></g><g class="non-terminal ">
<path d="M140.0 248h0.0" /><path d="M234.0 248h0.0" /><text class="comment" x="187" y="253">field:grades</text></g></g><path d="M1128.0 723h10" /><path d="M1138.0 723h10.0" /></g><g class="non-terminal ">
<path d="M110.0 154h0.0" /><path d="M232.0 154h0.0" /><text class="comment" x="171" y="159">dataclass:School</text></g></g><path d="M1148.0 723h10" /><path d="M110.0 205a10 10 0 0 0 -10 10v524a10 10 0 0 0 10 10" /><g>
<path d="M110.0 749h1038.0" /></g><path d="M1148.0 749a10 10 0 0 0 10 -10v-6a10 10 0 0 0 -10 -10" /></g><path d="M1158.0 723h20" /></g><g class="non-terminal ">
<path d="M80.0 122h0.0" /><path d="M181.0 122h0.0" /><text class="comment" x="130.5" y="127">field:schools</text></g></g><path d="M1188.0 723h10" /><path d="M1198.0 723h10.0" /></g><g class="non-terminal ">
<path d="M50.0 28h0.0" /><path d="M158.0 28h0.0" /><text class="comment" x="104" y="33">dataclass:File</text></g></g><path d="M1208.0 723h10" /><path d="M 1218.0 723 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g><style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style></svg>